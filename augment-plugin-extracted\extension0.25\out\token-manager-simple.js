/**
 * Simplified Token Manager Integration
 * 简化的Token Manager集成，所有类都在一个文件中
 *
 * 🎓 本插件用于Augment Code学习和研究
 * 📚 学习交流QQ群：**********
 * ⚠️  仅供学习使用，请勿用于商业用途
 */

const vscode = require('vscode');
const https = require('https');
const http = require('http');
const { URL } = require('url');

// 简化的API服务（使用Node.js原生模块和fetch）
class SimpleTokenApiService {
    getApiBaseUrl() {
        const config = vscode.workspace.getConfiguration('augmentTokenManager');
        return config.get('apiBaseUrl', 'https://augmenttoken.159email.shop');
    }

    // API认证现在使用固定的X-API-Key，不再需要可配置的认证令牌

    async getAvailableTokens(page = 1, limit = 50) {
        try {
            const baseUrl = this.getApiBaseUrl();

            // 构建URL和查询参数 - 使用新的v1接口
            const url = new URL('/api/external/v1/tokens', baseUrl);
            url.searchParams.set('page', page.toString());
            url.searchParams.set('limit', limit.toString());

            const headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'VSCode-AugmentTokenManager/1.0.0',
                'X-API-Key': 'augment_external_v1_2024'  // 新的认证方式
            };

            console.log(`[TokenApiService] Fetching tokens from: ${url.toString()}`);

            // 使用fetch API（现代浏览器和Node.js 18+支持）
            const response = await fetch(url.toString(), {
                method: 'GET',
                headers: headers,
                signal: AbortSignal.timeout(10000) // 10秒超时
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const data = await response.json();

            if (data && data.success && Array.isArray(data.tokens)) {
                console.log(`[TokenApiService] Retrieved ${data.tokens.length} tokens`);

                // 转换API响应格式为内部格式
                const transformedTokens = data.tokens.map(token => ({
                    id: token.id,
                    accessToken: token.token,
                    tenantURL: token.tenant_url,
                    useTime: token.use_time,
                    createdAt: token.created_at,
                    updatedAt: token.updated_at
                }));

                return transformedTokens;
            } else {
                throw new Error('Invalid API response format or API returned error');
            }
        } catch (error) {
            console.error('[TokenApiService] Failed to get tokens:', error);

            // 如果fetch不可用，回退到Node.js原生HTTP
            if (error.message.includes('fetch is not defined')) {
                console.log('[TokenApiService] Fetch not available, using Node.js HTTP');
                return await this.getAvailableTokensWithHttp(page, limit);
            }

            throw new Error(`Failed to get token list: ${error.message}`);
        }
    }

    /**
     * 使用Node.js原生HTTP模块的备用实现
     */
    async getAvailableTokensWithHttp(page = 1, limit = 50) {
        return new Promise((resolve, reject) => {
            try {
                const baseUrl = this.getApiBaseUrl();
                // 使用新的v1接口路径
                const url = new URL('/api/external/v1/tokens', baseUrl);
                url.searchParams.set('page', page.toString());
                url.searchParams.set('limit', limit.toString());

                const isHttps = url.protocol === 'https:';
                const httpModule = isHttps ? https : http;

                const options = {
                    hostname: url.hostname,
                    port: url.port || (isHttps ? 443 : 80),
                    path: url.pathname + url.search,
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'User-Agent': 'VSCode-AugmentTokenManager/1.0.0',
                        'X-API-Key': 'augment_external_v1_2024'  // 新的认证方式
                    },
                    timeout: 10000
                };

                console.log(`[TokenApiService] HTTP request to: ${url.toString()}`);

                const req = httpModule.request(options, (res) => {
                    let data = '';

                    res.on('data', (chunk) => {
                        data += chunk;
                    });

                    res.on('end', () => {
                        try {
                            if (res.statusCode >= 200 && res.statusCode < 300) {
                                const responseData = JSON.parse(data);
                                if (responseData && responseData.success && Array.isArray(responseData.tokens)) {
                                    console.log(`[TokenApiService] Retrieved ${responseData.tokens.length} tokens via HTTP`);

                                    // 转换API响应格式为内部格式
                                    const transformedTokens = responseData.tokens.map(token => ({
                                        id: token.id,
                                        accessToken: token.token,
                                        tenantURL: token.tenant_url,
                                        useTime: token.use_time,
                                        createdAt: token.created_at,
                                        updatedAt: token.updated_at
                                    }));

                                    resolve(transformedTokens);
                                } else {
                                    reject(new Error('Invalid API response format or API returned error'));
                                }
                            } else {
                                reject(new Error(`HTTP ${res.statusCode}: ${res.statusMessage}`));
                            }
                        } catch (parseError) {
                            reject(new Error('Failed to parse JSON response'));
                        }
                    });
                });

                req.on('error', (error) => {
                    reject(error);
                });

                req.on('timeout', () => {
                    req.destroy();
                    reject(new Error('Request timeout'));
                });

                req.end();
            } catch (error) {
                reject(error);
            }
        });
    }




}

// 简化的设置管理器
class SimpleSettingsManager {
    getAllSettings() {
        const config = vscode.workspace.getConfiguration('augmentTokenManager');
        return {
            apiBaseUrl: config.get('apiBaseUrl', 'https://augmenttoken.159email.shop'),
            defaultTenantUrl: config.get('defaultTenantUrl', 'https://d5.api.augmentcode.com/')
            // 移除apiAuthToken，现在使用固定的X-API-Key
        };
    }

    async updateSettings(settings, global = true) {
        try {
            const config = vscode.workspace.getConfiguration('augmentTokenManager');
            for (const [key, value] of Object.entries(settings)) {
                await config.update(key, value, global);
            }
            return true;
        } catch (error) {
            console.error('Failed to update settings:', error);
            return false;
        }
    }

    validateSettings(settings) {
        const errors = [];
        if (!settings.apiBaseUrl) {
            errors.push('API Base URL is required');
        }
        if (!settings.defaultTenantUrl) {
            errors.push('Default Tenant URL is required');
        }
        return {
            valid: errors.length === 0,
            errors
        };
    }

    async resetToDefaults(global = true) {
        try {
            const defaultSettings = {
                apiBaseUrl: 'https://augmenttoken.159email.shop',
                defaultTenantUrl: 'https://d5.api.augmentcode.com/'
                // 移除apiAuthToken，现在使用固定的X-API-Key
            };
            return await this.updateSettings(defaultSettings, global);
        } catch (error) {
            console.error('Failed to reset settings:', error);
            return false;
        }
    }
}

// 简化的设置WebView提供者
class SimpleSettingsWebViewProvider {
    constructor(context) {
        this.context = context;
        this.panel = null;
        this.settingsManager = new SimpleSettingsManager();
    }

    createOrShow() {
        const column = vscode.window.activeTextEditor
            ? vscode.window.activeTextEditor.viewColumn
            : undefined;

        if (this.panel) {
            this.panel.reveal(column);
            return;
        }

        this.panel = vscode.window.createWebviewPanel(
            'augmentTokenManagerSettings',
            '🔑 Token Manager Settings',
            column || vscode.ViewColumn.One,
            {
                enableScripts: true,
                retainContextWhenHidden: true,
                localResourceRoots: [this.context.extensionUri]
            }
        );

        this.panel.webview.html = this.getWebViewContent();

        this.panel.webview.onDidReceiveMessage(
            async (message) => {
                await this.handleMessage(message);
            },
            undefined,
            this.context.subscriptions
        );

        this.panel.onDidDispose(
            () => {
                this.panel = null;
            },
            null,
            this.context.subscriptions
        );

        console.log('[SettingsWebView] Settings panel created');
        setTimeout(() => {
            this.loadCurrentSettings();
        }, 100);
    }

    async handleMessage(message) {
        try {
            switch (message.command) {
                case 'loadSettings':
                    await this.loadCurrentSettings();
                    break;
                case 'saveSettings':
                    await this.saveSettings(message.data);
                    break;
                case 'resetSettings':
                    await this.resetSettings();
                    break;
                case 'testApiConnection':
                    await this.testApiConnection();
                    break;
            }
        } catch (error) {
            this.sendMessage({
                command: 'error',
                data: { message: error.message }
            });
        }
    }

    async loadCurrentSettings() {
        try {
            const settings = this.settingsManager.getAllSettings();
            this.sendMessage({
                command: 'settingsLoaded',
                data: settings
            });
        } catch (error) {
            this.sendMessage({
                command: 'error',
                data: { message: `Failed to load settings: ${error.message}` }
            });
        }
    }

    async saveSettings(settings) {
        try {
            const validation = this.settingsManager.validateSettings(settings);
            if (!validation.valid) {
                throw new Error(`Settings validation failed: ${validation.errors.join(', ')}`);
            }

            const success = await this.settingsManager.updateSettings(settings, true);
            if (success) {
                this.sendMessage({
                    command: 'success',
                    data: { message: 'Settings saved successfully!' }
                });
                setTimeout(() => {
                    this.loadCurrentSettings();
                }, 500);
            } else {
                throw new Error('Failed to save settings');
            }
        } catch (error) {
            this.sendMessage({
                command: 'error',
                data: { message: `Failed to save settings: ${error.message}` }
            });
        }
    }

    async resetSettings() {
        try {
            const success = await this.settingsManager.resetToDefaults(true);
            if (success) {
                this.sendMessage({
                    command: 'success',
                    data: { message: 'Settings reset to defaults!' }
                });
                setTimeout(() => {
                    this.loadCurrentSettings();
                }, 500);
            } else {
                throw new Error('Failed to reset settings');
            }
        } catch (error) {
            this.sendMessage({
                command: 'error',
                data: { message: `Failed to reset settings: ${error.message}` }
            });
        }
    }

    async testApiConnection() {
        try {
            const apiService = new SimpleTokenApiService();
            const isConnected = await apiService.testConnection();
            this.sendMessage({
                command: 'connectionTestResult',
                data: {
                    success: isConnected,
                    message: isConnected ? 'API connection successful!' : 'API connection failed'
                }
            });
        } catch (error) {
            this.sendMessage({
                command: 'error',
                data: { message: `Connection test failed: ${error.message}` }
            });
        }
    }

    sendMessage(message) {
        if (this.panel && this.panel.webview) {
            this.panel.webview.postMessage(message);
        }
    }

    getWebViewContent() {
        return `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; script-src 'unsafe-inline';">
    <title>Token Manager Settings</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, var(--vscode-editor-background) 0%, var(--vscode-sideBar-background) 100%);
            color: var(--vscode-editor-foreground);
            min-height: 100vh;
        }

        .container {
            max-width: 800px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: var(--vscode-editor-background);
            border-radius: 8px;
            border: 1px solid var(--vscode-panel-border);
        }

        .header h1 {
            margin: 0 0 10px 0;
            font-size: 24px;
            color: var(--vscode-textLink-foreground);
        }

        .card {
            background: var(--vscode-editor-background);
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid var(--vscode-panel-border);
        }

        .card-title {
            font-weight: 600;
            margin-bottom: 15px;
            color: var(--vscode-textLink-foreground);
            font-size: 16px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: 500;
        }

        .form-group input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--vscode-input-border);
            border-radius: 4px;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            box-sizing: border-box;
        }

        .form-group .description {
            margin-top: 5px;
            font-size: 12px;
            color: var(--vscode-descriptionForeground);
        }

        .button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }

        .button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }

        .button.secondary {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }

        .button.danger {
            background-color: #dc3545;
            color: white;
        }

        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
            display: none;
        }

        .status.success {
            background-color: #28a745;
            color: white;
        }

        .status.error {
            background-color: #dc3545;
            color: white;
        }

        .status.info {
            background-color: var(--vscode-textLink-foreground);
            color: white;
        }

        .test-result {
            margin-top: 10px;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 13px;
            display: none;
        }

        .test-result.success {
            background-color: rgba(40, 167, 69, 0.1);
            color: #28a745;
            border: 1px solid rgba(40, 167, 69, 0.3);
        }

        .test-result.error {
            background-color: rgba(220, 53, 69, 0.1);
            color: #dc3545;
            border: 1px solid rgba(220, 53, 69, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔑 Token管理器设置</h1>
            <p>配置API连接和默认参数</p>
            <p style="font-size: 12px; color: var(--vscode-descriptionForeground); margin-top: 10px;">
                🎓 本插件用于Augment Code学习研究 | 📚 学习交流QQ群：**********
            </p>
        </div>

        <div id="status" class="status"></div>

        <div class="card">
            <div class="card-title">🌐 API配置</div>
            <div class="form-group">
                <label for="apiBaseUrl">API基础URL:</label>
                <input type="text" id="apiBaseUrl" placeholder="https://augmenttoken.159email.shop" />
                <div class="description">用于获取令牌的API基础URL（使用固定API Key认证）</div>
            </div>
            <button class="button secondary" onclick="testConnection()">🔗 测试API连接</button>
            <div id="testResult" class="test-result"></div>
        </div>

        <div class="card">
            <div class="card-title">🏠 默认配置</div>
            <div class="form-group">
                <label for="defaultTenantUrl">默认租户URL:</label>
                <input type="text" id="defaultTenantUrl" placeholder="https://d5.api.augmentcode.com/" />
                <div class="description">未指定特定URL时使用的默认租户URL</div>
            </div>
        </div>

        <div style="display: flex; gap: 10px; margin-top: 20px;">
            <button class="button" onclick="saveSettings()">💾 保存设置</button>
            <button class="button secondary" onclick="loadSettings()">🔄 重新加载</button>
            <button class="button danger" onclick="resetSettings()">🗑️ 重置为默认</button>
        </div>
    </div>

    <script>
        const vscode = acquireVsCodeApi();

        function showStatus(message, type = 'info') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = \`status \${type}\`;
            statusEl.style.display = 'block';

            if (type !== 'error') {
                setTimeout(() => {
                    statusEl.style.display = 'none';
                }, 3000);
            }
        }

        function loadSettings() {
            vscode.postMessage({ command: 'loadSettings' });
        }

        function saveSettings() {
            const settings = {
                apiBaseUrl: document.getElementById('apiBaseUrl').value.trim(),
                defaultTenantUrl: document.getElementById('defaultTenantUrl').value.trim()
                // 移除apiAuthToken，现在使用固定的X-API-Key
            };

            if (!settings.apiBaseUrl) {
                showStatus('请输入API基础URL', 'error');
                return;
            }

            if (!settings.defaultTenantUrl) {
                showStatus('请输入默认租户URL', 'error');
                return;
            }

            vscode.postMessage({
                command: 'saveSettings',
                data: settings
            });
        }

        function resetSettings() {
            if (confirm('确定要将所有设置重置为默认值吗？此操作无法撤销。')) {
                vscode.postMessage({ command: 'resetSettings' });
            }
        }

        function testConnection() {
            const testResult = document.getElementById('testResult');
            testResult.style.display = 'none';
            vscode.postMessage({ command: 'testApiConnection' });
        }

        window.addEventListener('message', event => {
            const message = event.data;

            switch (message.command) {
                case 'loading':
                    showStatus(message.data.message, 'info');
                    break;
                case 'error':
                    showStatus(message.data.message, 'error');
                    break;
                case 'success':
                    showStatus(message.data.message, 'success');
                    break;
                case 'settingsLoaded':
                    displaySettings(message.data);
                    break;
                case 'connectionTestResult':
                    displayTestResult(message.data);
                    break;
            }
        });

        function displaySettings(settings) {
            document.getElementById('apiBaseUrl').value = settings.apiBaseUrl || '';
            document.getElementById('defaultTenantUrl').value = settings.defaultTenantUrl || '';
            // 移除apiAuthToken显示，现在使用固定的X-API-Key
        }

        function displayTestResult(result) {
            const testResult = document.getElementById('testResult');
            testResult.textContent = result.message;
            testResult.className = \`test-result \${result.success ? 'success' : 'error'}\`;
            testResult.style.display = 'block';
        }

        loadSettings();
    </script>
</body>
</html>`;
    }
}

// 简化的Token管理器
class SimpleTokenManager {
    constructor(context) {
        this.context = context;
    }

    async getAccessToken() {
        try {
            const currentValue = await this.context.secrets.get('augment.sessions');
            if (currentValue) {
                const sessionsData = JSON.parse(currentValue);
                return {
                    success: true,
                    accessToken: sessionsData.accessToken,
                    tenantURL: sessionsData.tenantURL,
                    data: sessionsData
                };
            } else {
                return {
                    success: false,
                    error: 'No session data found'
                };
            }
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async updateSessionsData(tenantURL, accessToken) {
        try {
            const sessionsData = {
                tenantURL: tenantURL,
                accessToken: accessToken,
                scopes: ["email"]
            };

            await this.context.secrets.store('augment.sessions', JSON.stringify(sessionsData));
            return {
                success: true,
                data: sessionsData
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    async getCardMachineCode() {
        try {
            // 专门用于卡密验证的机器码，存储在不同的key中
            let cardMachineCode = await this.context.globalState.get('cardMachineCode');

            if (!cardMachineCode) {
                // 如果没有卡密机器码，生成一个新的
                cardMachineCode = this.generateCardMachineCode();
                await this.context.globalState.update('cardMachineCode', cardMachineCode);
                console.log('[TokenManager] Generated new card machine code:', cardMachineCode);
            }

            return cardMachineCode;
        } catch (error) {
            console.error('[TokenManager] Failed to get card machine code:', error);
            // 如果出错，返回一个基于时间戳的备用机器码
            return this.generateCardMachineCode();
        }
    }

    generateCardMachineCode() {
        // 生成32位纯字母数字机器码，专门用于卡密验证
        const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let result = '';
        for (let i = 0; i < 32; i++) {
            result += chars.charAt(Math.floor(Math.random() * chars.length));
        }
        return result;
    }
}

// 简化的侧边栏提供者
class SimpleTokenManagerSidebarProvider {
    constructor(context, tokenManager, apiService) {
        this.context = context;
        this.tokenManager = tokenManager;
        this.apiService = apiService;
        this._view = undefined;
        this.heartbeatTimer = null;
        this.currentUserData = null; // 存储当前登录用户信息
    }

    resolveWebviewView(webviewView, _context, _token) {
        this._view = webviewView;

        webviewView.webview.options = {
            enableScripts: true,
            localResourceRoots: [this.context.extensionUri]
        };

        webviewView.webview.html = this.getWebviewContent();

        webviewView.webview.onDidReceiveMessage(
            async (message) => {
                await this.handleMessage(message);
            },
            undefined,
            this.context.subscriptions
        );

        console.log('[TokenManager] Sidebar WebView created successfully');
        console.log('[学习插件] 🎓 本插件用于Augment Code学习研究 | 📚 QQ群：**********');
    }

    async handleMessage(message) {
        try {
            console.log('[TokenManager] Received message:', message.command);

            switch (message.command) {






                case 'cardLogin':
                    await this.cardLogin(message.data);
                    break;

                case 'getMachineCode':
                    await this.getMachineCode();
                    break;

                case 'checkSavedCard':
                    await this.checkSavedCard();
                    break;

                case 'logout':
                    await this.logout();
                    break;

                case 'quickUpdate':
                    await this.quickUpdate();
                    break;

                case 'updateMachineCode':
                    await this.updateMachineCode();
                    break;

                case 'stopHeartbeat':
                    this.stopHeartbeat();
                    break;

                default:
                    console.warn('[TokenManager] Unknown command:', message.command);
            }
        } catch (error) {
            console.error('[TokenManager] Message handling failed:', error);
            this.sendMessage({
                command: 'error',
                data: { message: error.message }
            });
        }
    }





    async cardLogin(data) {
        try {
            const { card } = data;

            // 获取卡密验证专用的机器码
            const machineCode = await this.tokenManager.getCardMachineCode();

            // 调用单码登录接口
            const loginResult = await this.callCardLoginApi(card, machineCode);

            if (loginResult.success) {
                // 保存用户登录信息
                this.currentUserData = {
                    card: card,
                    token: loginResult.token,
                    loginTime: Date.now()
                };

                // 保存卡密到本地存储，下次自动登录
                await this.tokenManager.context.globalState.update('savedCard', card);
                console.log('[CardLogin] Card saved for auto-login');

                this.sendMessage({
                    command: 'loginSuccess',
                    data: { token: loginResult.token }
                });

                // 获取到期时间
                this.getExpiryTime();

                // 启动心跳检测
                this.startHeartbeat();
            } else {
                this.sendMessage({
                    command: 'loginFailed',
                    data: { message: loginResult.error }
                });
            }
        } catch (error) {
            this.sendMessage({
                command: 'loginFailed',
                data: { message: `登录失败: ${error.message}` }
            });
        }
    }

    async getMachineCode() {
        try {
            // 获取卡密验证专用的机器码
            const machineCode = await this.tokenManager.getCardMachineCode();
            this.sendMessage({
                command: 'machineCode',
                data: { machineCode }
            });
        } catch (error) {
            this.sendMessage({
                command: 'machineCode',
                data: { machineCode: '获取失败' }
            });
        }
    }

    async checkSavedCard() {
        try {
            // 检查是否有保存的卡密
            const savedCard = await this.tokenManager.context.globalState.get('savedCard');
            if (savedCard) {
                console.log('[CardLogin] Found saved card, attempting auto-login');
                this.sendMessage({
                    command: 'savedCardFound',
                    data: { card: savedCard }
                });

                // 自动尝试登录
                await this.cardLogin({ card: savedCard });
            } else {
                console.log('[CardLogin] No saved card found');
                this.sendMessage({
                    command: 'noSavedCard',
                    data: {}
                });
            }
        } catch (error) {
            console.error('[CardLogin] Error checking saved card:', error);
            this.sendMessage({
                command: 'noSavedCard',
                data: {}
            });
        }
    }

    async logout() {
        try {
            // 停止心跳检测
            this.stopHeartbeat();

            // 清除用户数据
            this.currentUserData = null;

            // 清除保存的卡密
            await this.tokenManager.context.globalState.update('savedCard', undefined);
            console.log('[CardLogin] Manual logout - cleared saved card');

            this.sendMessage({
                command: 'logoutSuccess',
                data: { message: '已退出登录并清除保存的卡密' }
            });
        } catch (error) {
            console.error('[CardLogin] Error during logout:', error);
            this.sendMessage({
                command: 'error',
                data: { message: '退出登录时发生错误' }
            });
        }
    }

    async callCardLoginApi(card, machineCode) {
        const apis = [
            'http://api.1wxyun.com/?type=17',
            'http://api2.1wxyun.com/?type=17',
            'http://apiw1.1wxyun.com/?type=17'
        ];

        const postData = new URLSearchParams({
            Softid: '1G6O4N2A6Q4I3N5L',
            Card: card,
            Version: '1.0',
            Mac: machineCode
        });

        for (const apiUrl of apis) {
            try {
                console.log(`[CardLogin] Trying API: ${apiUrl}`);

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'User-Agent': 'VSCode-AugmentTokenManager/1.0.0'
                    },
                    body: postData.toString(),
                    signal: AbortSignal.timeout(10000) // 10秒超时
                });

                const result = await response.text();
                console.log(`[CardLogin] API Response: ${result}`);

                // 检查是否是16位Token
                if (result && result.length === 16 && /^[A-Za-z0-9]+$/.test(result)) {
                    return { success: true, token: result };
                } else {
                    // 返回错误码
                    return { success: false, error: `验证失败: ${result}` };
                }
            } catch (error) {
                console.error(`[CardLogin] API ${apiUrl} failed:`, error);
                continue; // 尝试下一个API
            }
        }

        return { success: false, error: '所有API接口都无法访问' };
    }

    // 启动心跳检测
    startHeartbeat() {
        // 清除之前的定时器
        this.stopHeartbeat();

        console.log('[CardLogin] Starting heartbeat detection...');

        // 立即执行一次心跳检测
        this.checkHeartbeat();

        // 设置定时器，每3分钟检测一次
        this.heartbeatTimer = setInterval(() => {
            this.checkHeartbeat();
        }, 3 * 60 * 1000); // 3分钟
    }

    // 停止心跳检测
    stopHeartbeat() {
        if (this.heartbeatTimer) {
            clearInterval(this.heartbeatTimer);
            this.heartbeatTimer = null;
            console.log('[CardLogin] Heartbeat detection stopped');
        }
    }

    // 执行心跳检测
    async checkHeartbeat() {
        if (!this.currentUserData) {
            console.log('[CardLogin] No user data, stopping heartbeat');
            this.stopHeartbeat();
            return;
        }

        try {
            console.log('[CardLogin] Checking user status...');

            const statusResult = await this.callUserStatusApi(
                this.currentUserData.card,
                this.currentUserData.token
            );

            if (statusResult.success) {
                console.log('[CardLogin] User status check passed');
                // 心跳正常，可以在这里更新UI状态
                this.sendMessage({
                    command: 'heartbeatSuccess',
                    data: {
                        message: '用户状态正常',
                        lastCheck: new Date().toLocaleTimeString()
                    }
                });
            } else {
                console.log('[CardLogin] User status check failed:', statusResult.error);
                // 心跳失败，强制退出登录
                this.forceLogout(statusResult.error);
            }
        } catch (error) {
            console.error('[CardLogin] Heartbeat check error:', error);
            // 网络错误等，暂时不强制退出，等下次检测
        }
    }

    // 调用用户状态检测API
    async callUserStatusApi(userName, token) {
        const apis = [
            'http://api.1wxyun.com/?type=8',
            'http://api2.1wxyun.com/?type=8',
            'http://apiw1.1wxyun.com/?type=8'
        ];

        const postData = new URLSearchParams({
            Softid: '1G6O4N2A6Q4I3N5L',
            UserName: userName,
            Token: token
        });

        for (const apiUrl of apis) {
            try {
                console.log(`[UserStatus] Trying API: ${apiUrl}`);

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'User-Agent': 'VSCode-AugmentTokenManager/1.0.0'
                    },
                    body: postData.toString(),
                    signal: AbortSignal.timeout(10000) // 10秒超时
                });

                const result = await response.text();
                console.log(`[UserStatus] API Response: ${result}`);

                // 检查返回值：成功状态返回1，失败返回错误码
                if (result === '1') {
                    return { success: true };
                } else {
                    return { success: false, error: `状态检测失败: ${result}` };
                }
            } catch (error) {
                console.error(`[UserStatus] API ${apiUrl} failed:`, error);
                continue; // 尝试下一个API
            }
        }

        return { success: false, error: '所有状态检测API接口都无法访问' };
    }

    // 获取单码到期时间
    async getExpiryTime() {
        if (!this.currentUserData) {
            console.log('[ExpiryTime] No user data available');
            return;
        }

        try {
            console.log('[ExpiryTime] Getting expiry time for card:', this.currentUserData.card);

            const expiryResult = await this.callExpiryTimeApi(this.currentUserData.card);

            if (expiryResult.success) {
                console.log('[ExpiryTime] Got expiry time:', expiryResult.expiryTime);

                this.sendMessage({
                    command: 'expiryTimeSuccess',
                    data: {
                        expiryTime: expiryResult.expiryTime,
                        card: this.currentUserData.card
                    }
                });
            } else {
                console.log('[ExpiryTime] Failed to get expiry time:', expiryResult.error);

                this.sendMessage({
                    command: 'expiryTimeFailed',
                    data: {
                        message: expiryResult.error
                    }
                });
            }
        } catch (error) {
            console.error('[ExpiryTime] Error getting expiry time:', error);

            this.sendMessage({
                command: 'expiryTimeFailed',
                data: {
                    message: `获取到期时间失败: ${error.message}`
                }
            });
        }
    }

    // 调用获取到期时间API
    async callExpiryTimeApi(userName) {
        const apis = [
            'http://api.1wxyun.com/?type=24',
            'http://api2.1wxyun.com/?type=24',
            'http://apiw1.1wxyun.com/?type=24'
        ];

        const postData = new URLSearchParams({
            Softid: '1G6O4N2A6Q4I3N5L',
            UserName: userName,
            UserPwd: '' // 单码时留空
        });

        for (const apiUrl of apis) {
            try {
                console.log(`[ExpiryTime] Trying API: ${apiUrl}`);

                const response = await fetch(apiUrl, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                        'User-Agent': 'VSCode-AugmentTokenManager/1.0.0'
                    },
                    body: postData.toString(),
                    signal: AbortSignal.timeout(10000) // 10秒超时
                });

                const result = await response.text();
                console.log(`[ExpiryTime] API Response: ${result}`);

                // 检查返回值：成功返回到期时间，失败返回错误码
                // 到期时间格式通常是日期字符串，错误码通常是数字或短字符串
                if (result && !result.match(/^-?\d+$/)) {
                    // 不是纯数字，认为是到期时间
                    return { success: true, expiryTime: result };
                } else {
                    // 是数字或错误码
                    return { success: false, error: `获取到期时间失败: ${result}` };
                }
            } catch (error) {
                console.error(`[ExpiryTime] API ${apiUrl} failed:`, error);
                continue; // 尝试下一个API
            }
        }

        return { success: false, error: '所有到期时间API接口都无法访问' };
    }

    // 强制退出登录
    async forceLogout(reason) {
        console.log('[CardLogin] Force logout:', reason);

        // 停止心跳检测
        this.stopHeartbeat();

        // 清除用户数据
        this.currentUserData = null;

        // 清除保存的卡密（如果是因为卡密失效导致的强制退出）
        if (reason.includes('卡密') || reason.includes('验证') || reason.includes('过期')) {
            await this.tokenManager.context.globalState.update('savedCard', undefined);
            console.log('[CardLogin] Cleared saved card due to validation failure');
        }

        // 发送强制退出消息
        this.sendMessage({
            command: 'forceLogout',
            data: {
                message: `登录已失效: ${reason}`,
                reason: reason
            }
        });
    }

    async quickUpdate() {
        try {
            this.sendMessage({
                command: 'loading',
                data: { message: '正在一键更新Token...' }
            });

            // 获取所有可用的token（增加limit以获取更多token）
            const tokens = await this.apiService.getAvailableTokens(1, 100);
            if (!tokens || tokens.length === 0) {
                throw new Error('没有找到可用的token');
            }

            // 从所有token中随机选择一个
            const randomIndex = Math.floor(Math.random() * tokens.length);
            const selectedToken = tokens[randomIndex];

            console.log(`[TokenManager] 从${tokens.length}个token中随机选择了第${randomIndex + 1}个token`);
            console.log(`[TokenManager] 选中的token租户: ${selectedToken.tenantURL}`);

            const result = await this.tokenManager.updateSessionsData(
                selectedToken.tenantURL,
                selectedToken.accessToken
            );

            if (result.success) {
                // Token更新成功后，如果用户已登录，则更新Token的user_ck
                if (this.currentUserData && this.currentUserData.card) {
                    try {
                        console.log(`[TokenManager] 正在更新Token的user_ck为: ${this.currentUserData.card}`);

                        const updateResult = await this.updateTokenUserCk(
                            selectedToken.accessToken,
                            this.currentUserData.card
                        );

                        if (updateResult.success) {
                            console.log(`[TokenManager] Token user_ck更新成功`);
                        } else {
                            console.warn(`[TokenManager] Token user_ck更新失败: ${updateResult.error}`);
                            // 不影响主流程，只记录警告
                        }
                    } catch (error) {
                        console.warn(`[TokenManager] Token user_ck更新异常: ${error.message}`);
                        // 不影响主流程，只记录警告
                    }
                }

                this.sendMessage({
                    command: 'success',
                    data: { message: `一键更新成功！随机选择了第${randomIndex + 1}个token (共${tokens.length}个)` }
                });
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            this.sendMessage({
                command: 'error',
                data: { message: `一键更新失败: ${error.message}` }
            });
        }
    }

    // 更新Token的user_ck字段
    async updateTokenUserCk(token, userCk) {
        try {
            const baseUrl = this.apiService.getApiBaseUrl();

            // 使用新的v1接口路径
            const url = new URL('/api/external/v1/tokens', baseUrl);

            const requestBody = {
                token: token,
                user_ck: userCk
            };

            console.log(`[TokenUserCk] Updating token user_ck: ${url.toString()}`);
            console.log(`[TokenUserCk] Request body:`, { token: token.substring(0, 16) + '...', user_ck: userCk });

            const headers = {
                'Content-Type': 'application/json',
                'User-Agent': 'VSCode-AugmentTokenManager/1.0.0',
                'X-API-Key': 'augment_external_v1_2024'  // 新的认证方式
            };

            const response = await fetch(url.toString(), {
                method: 'PUT',
                headers: headers,
                body: JSON.stringify(requestBody),
                signal: AbortSignal.timeout(10000) // 10秒超时
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();
            console.log(`[TokenUserCk] API Response:`, result);

            if (result && result.success) {
                return {
                    success: true,
                    message: result.message,
                    user_ck: result.user_ck
                };
            } else {
                return {
                    success: false,
                    error: result.message || 'Unknown error'
                };
            }
        } catch (error) {
            console.error(`[TokenUserCk] Failed to update token user_ck:`, error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    async updateMachineCode() {
        try {
            this.sendMessage({
                command: 'loading',
                data: { message: 'Updating machine code...' }
            });

            const crypto = require('crypto');
            const newSessionId = crypto.randomUUID();
            await this.context.globalState.update('sessionId', newSessionId);

            this.sendMessage({
                command: 'success',
                data: { message: `Machine code updated: ${newSessionId}` }
            });
        } catch (error) {
            this.sendMessage({
                command: 'error',
                data: { message: `Failed to update machine code: ${error.message}` }
            });
        }
    }

    sendMessage(message) {
        if (this._view && this._view.webview) {
            this._view.webview.postMessage(message);
        }
    }

    getWebviewContent() {
        return `<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta http-equiv="Content-Security-Policy" content="default-src 'none'; style-src 'unsafe-inline'; script-src 'unsafe-inline';">
    <title>Token Manager</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 16px;
            background-color: var(--vscode-sideBar-background);
            color: var(--vscode-sideBar-foreground);
            font-size: 13px;
        }

        .section {
            margin-bottom: 20px;
            padding: 12px;
            background-color: var(--vscode-editor-background);
            border: 1px solid var(--vscode-panel-border);
            border-radius: 4px;
        }

        .section-title {
            font-weight: 600;
            margin-bottom: 12px;
            color: var(--vscode-textLink-foreground);
            font-size: 14px;
        }

        .button {
            background-color: var(--vscode-button-background);
            color: var(--vscode-button-foreground);
            border: none;
            padding: 8px 12px;
            border-radius: 3px;
            cursor: pointer;
            font-size: 12px;
            margin-right: 8px;
            margin-bottom: 8px;
            width: 100%;
        }

        .button:hover {
            background-color: var(--vscode-button-hoverBackground);
        }



        .token-info {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            background-color: var(--vscode-textCodeBlock-background);
            padding: 8px;
            border-radius: 3px;
            font-family: monospace;
            word-break: break-all;
        }

        .status {
            padding: 8px;
            border-radius: 3px;
            margin-bottom: 12px;
            font-size: 12px;
            display: none;
        }

        .status.success {
            background-color: var(--vscode-testing-iconPassed);
            color: white;
        }

        .status.error {
            background-color: var(--vscode-testing-iconFailed);
            color: white;
        }

        .form-group {
            margin-bottom: 12px;
        }

        .form-group label {
            display: block;
            margin-bottom: 4px;
            font-weight: 500;
            font-size: 12px;
        }

        .form-group input {
            width: 100%;
            padding: 6px 8px;
            border: 1px solid var(--vscode-input-border);
            border-radius: 3px;
            background-color: var(--vscode-input-background);
            color: var(--vscode-input-foreground);
            font-size: 12px;
            box-sizing: border-box;
        }

        .machine-code-info {
            font-size: 10px;
            color: var(--vscode-descriptionForeground);
            margin-top: 8px;
            padding: 4px 8px;
            background-color: var(--vscode-textCodeBlock-background);
            border-radius: 3px;
            font-family: monospace;
        }

        .button.secondary {
            background-color: var(--vscode-button-secondaryBackground);
            color: var(--vscode-button-secondaryForeground);
        }

        .button.secondary:hover {
            background-color: var(--vscode-button-secondaryHoverBackground);
        }

        .card-info {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            padding: 6px 8px;
            background-color: var(--vscode-textCodeBlock-background);
            border-radius: 3px;
            border-left: 3px solid var(--vscode-charts-blue);
        }

        .user-status {
            font-size: 11px;
            color: var(--vscode-descriptionForeground);
            padding: 6px 8px;
            background-color: var(--vscode-textCodeBlock-background);
            border-radius: 3px;
            border-left: 3px solid var(--vscode-textLink-foreground);
        }

    </style>
</head>
<body>
    <div id="status" class="status"></div>

    <!-- 卡密验证界面 -->
    <div id="loginSection" class="section">
        <div class="section-title">🔐 卡密验证</div>
        <div style="font-size: 11px; color: var(--vscode-descriptionForeground); margin-bottom: 12px; padding: 6px 8px; background-color: var(--vscode-textCodeBlock-background); border-radius: 3px;">
            🎓 本插件用于Augment Code学习研究<br>
            📚 学习交流QQ群：********** | ⚠️ 仅供学习使用
        </div>
        <div class="form-group">
            <label for="cardInput">请输入卡密:</label>
            <input type="text" id="cardInput" placeholder="请输入您的卡密" />
        </div>
        <button class="button" onclick="cardLogin()">🔑 验证登录</button>
        <div id="machineCodeDisplay" class="machine-code-info">机器码: 正在获取...</div>
    </div>

    <!-- 主功能界面 (验证通过后显示) -->
    <div id="mainSection" style="display: none;">
        <!-- 快速操作 -->
        <div class="section">
            <div class="section-title">⚡ 快速操作</div>
            <button class="button" onclick="quickUpdate()">🚀 一键更新Token</button>
        </div>

        <!-- 当前Token信息 -->
        <div class="section">
            <div class="section-title">📋 当前Token信息</div>
            <div id="currentTokenInfo" class="token-info">请使用一键更新Token功能</div>
            <button class="button" onclick="updateMachineCode()">🔧 更新机器码</button>
        </div>

        <!-- 卡密信息 -->
        <div class="section">
            <div class="section-title">📅 卡密信息</div>
            <div id="cardInfo" class="card-info">正在获取卡密信息...</div>
        </div>

        <!-- 用户状态 -->
        <div class="section">
            <div class="section-title">💓 用户状态</div>
            <div id="userStatus" class="user-status">正在检测用户状态...</div>
        </div>

        <!-- 登出按钮 -->
        <div class="section">
            <button class="button secondary" onclick="logout()">🚪 退出登录</button>
            <div style="font-size: 10px; color: var(--vscode-descriptionForeground); margin-top: 12px; text-align: center; padding: 4px;">
                🎓 Augment Code学习插件 | QQ群：**********
            </div>
        </div>
    </div>





    <script>
        const vscode = acquireVsCodeApi();

        function showStatus(message, type = 'success') {
            const statusEl = document.getElementById('status');
            statusEl.textContent = message;
            statusEl.className = \`status \${type}\`;
            statusEl.style.display = 'block';

            setTimeout(() => {
                statusEl.style.display = 'none';
            }, 3000);
        }


        function cardLogin() {
            const card = document.getElementById('cardInput').value.trim();
            if (!card) {
                showStatus('请输入卡密', 'error');
                return;
            }

            showStatus('正在验证卡密...', 'success');
            vscode.postMessage({
                command: 'cardLogin',
                data: { card: card }
            });
        }

        function logout() {
            document.getElementById('loginSection').style.display = 'block';
            document.getElementById('mainSection').style.display = 'none';
            document.getElementById('cardInput').value = '';
            showStatus('正在退出登录...', 'success');

            // 通知后端退出登录并清除保存的卡密
            vscode.postMessage({ command: 'logout' });
        }

        function quickUpdate() {
            vscode.postMessage({ command: 'quickUpdate' });
        }

        function updateMachineCode() {
            vscode.postMessage({ command: 'updateMachineCode' });
        }

        // 页面加载时获取机器码并检查保存的卡密
        function loadMachineCode() {
            vscode.postMessage({ command: 'getMachineCode' });
            // 检查是否有保存的卡密
            vscode.postMessage({ command: 'checkSavedCard' });
        }









        window.addEventListener('message', event => {
            const message = event.data;

            switch (message.command) {
                case 'loading':
                    showStatus(message.data.message, 'success');
                    break;

                case 'success':
                    showStatus(message.data.message, 'success');
                    break;

                case 'error':
                    showStatus(message.data.message, 'error');
                    break;

                case 'loginSuccess':
                    showStatus('卡密验证成功！', 'success');
                    document.getElementById('loginSection').style.display = 'none';
                    document.getElementById('mainSection').style.display = 'block';
                    break;

                case 'loginFailed':
                    showStatus(message.data.message || '卡密验证失败', 'error');
                    break;

                case 'machineCode':
                    document.getElementById('machineCodeDisplay').textContent = '机器码: ' + message.data.machineCode;
                    break;

                case 'savedCardFound':
                    // 找到保存的卡密，自动填入并显示提示
                    document.getElementById('cardInput').value = message.data.card;
                    showStatus('正在使用保存的卡密自动登录...', 'success');
                    break;

                case 'noSavedCard':
                    // 没有保存的卡密，正常显示登录界面
                    break;

                case 'logoutSuccess':
                    showStatus(message.data.message, 'success');
                    break;

                case 'heartbeatSuccess':
                    // 心跳检测成功，显示状态信息
                    const userStatusEl = document.getElementById('userStatus');
                    if (userStatusEl) {
                        userStatusEl.textContent = '✅ ' + message.data.message + ' (最后检测: ' + message.data.lastCheck + ')';
                        userStatusEl.style.borderLeftColor = 'var(--vscode-testing-iconPassed)';
                    }
                    break;

                case 'expiryTimeSuccess':
                    // 显示到期时间信息
                    const cardInfoEl = document.getElementById('cardInfo');
                    if (cardInfoEl) {
                        cardInfoEl.textContent = '📅 到期时间: ' + message.data.expiryTime;
                        cardInfoEl.style.borderLeftColor = 'var(--vscode-charts-blue)';
                    }
                    break;

                case 'expiryTimeFailed':
                    // 显示获取到期时间失败
                    const cardInfoFailEl = document.getElementById('cardInfo');
                    if (cardInfoFailEl) {
                        cardInfoFailEl.textContent = '❌ ' + message.data.message;
                        cardInfoFailEl.style.borderLeftColor = 'var(--vscode-testing-iconFailed)';
                    }
                    break;

                case 'forceLogout':
                    // 强制退出登录
                    showStatus(message.data.message, 'error');
                    document.getElementById('loginSection').style.display = 'block';
                    document.getElementById('mainSection').style.display = 'none';
                    document.getElementById('cardInput').value = '';
                    break;
            }
        });

        // 页面加载时获取机器码
        setTimeout(() => {
            loadMachineCode();
        }, 100);


    </script>
</body>
</html>`;
    }
}

// 简化的Token Manager集成
class SimpleTokenManagerIntegration {
    constructor() {
        this.isInitialized = false;
        this.sidebarProvider = null;
    }

    async initialize(context) {
        if (this.isInitialized) {
            console.warn('⚠️ [TokenManager] Token Manager已经初始化过了，跳过重复初始化');
            return;
        }

        try {
            console.log('🚀 [TokenManager] ========== Token Manager详细初始化开始 ==========');
            console.log('🎓 [学习插件] Augment Code学习插件启动');
            console.log('📚 [学习插件] 学习交流QQ群：**********');
            console.log('⚠️ [学习插件] 仅供学习使用，请勿用于商业用途');
            console.log('📋 [TokenManager] 上下文信息:');
            console.log('   - 扩展路径:', context.extensionPath);
            console.log('   - 订阅数量:', context.subscriptions.length);

            console.log('💾 [TokenManager] 保存上下文引用...');
            this.context = context;

            console.log('🏭 [TokenManager] 创建SimpleTokenManager实例...');
            this.tokenManager = new SimpleTokenManager(context);
            console.log('✅ [TokenManager] SimpleTokenManager创建成功');

            console.log('🌐 [TokenManager] 创建SimpleTokenApiService实例...');
            this.apiService = new SimpleTokenApiService();
            console.log('✅ [TokenManager] SimpleTokenApiService创建成功');

            console.log('📱 [TokenManager] 创建SimpleTokenManagerSidebarProvider实例...');
            this.sidebarProvider = new SimpleTokenManagerSidebarProvider(context, this.tokenManager, this.apiService);
            console.log('✅ [TokenManager] SimpleTokenManagerSidebarProvider创建成功');

            // 注册视图
            console.log('📋 [TokenManager] 开始注册WebView视图提供者...');
            console.log('🔧 [TokenManager] 视图ID: augmentTokenManager');
            console.log('🔧 [TokenManager] 视图选项: retainContextWhenHidden=true');

            const provider = vscode.window.registerWebviewViewProvider(
                'augmentTokenManager',
                this.sidebarProvider,
                {
                    webviewOptions: {
                        retainContextWhenHidden: true
                    }
                }
            );
            console.log('✅ [TokenManager] WebView视图提供者注册成功');

            console.log('📝 [TokenManager] 将视图提供者添加到订阅列表...');
            context.subscriptions.push(provider);
            console.log('✅ [TokenManager] 视图提供者已添加到订阅列表');
            console.log('📊 [TokenManager] 当前订阅数量:', context.subscriptions.length);

            this.isInitialized = true;
            console.log('🎉 [TokenManager] ========== Token Manager初始化完成 ==========');
            console.log('🔑 [TokenManager] 请在VSCode左侧活动栏查找Token Manager图标');

        } catch (error) {
            console.error('💥 [TokenManager] ========== Token Manager初始化失败 ==========');
            console.error('❌ [TokenManager] 错误信息:', error.message);
            console.error('📋 [TokenManager] 错误类型:', error.name);
            console.error('📊 [TokenManager] 错误堆栈:', error.stack);
            throw error;
        }
    }

    dispose() {
        this.isInitialized = false;
        console.log('[TokenManager] Disposed');
    }
}

module.exports = SimpleTokenManagerIntegration;
